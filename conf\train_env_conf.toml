# 环境配置
[env_conf]
# 地图名称不可修改，请使用默认名称。
map_name='map_fish'

# 地图配置
[env_conf.map_fish]
# 起点位置，仅在start_random=false时生效。
# 数组，包含两个元素，分别为xy坐标，取值范围为0~127。
start=[49, 26]

# 起点是否随机。
# 布尔值，ture表示随机起点，false表示使用start字段生成固定起点位置。
start_random=true

# 终点位置，仅在end_random=false时生效，不能与起点位置重复。
# 数组，包含两个元素，分别为xy坐标，取值范围为0~127。
end=[80, 114]

# 终点是否随机。
# 布尔值，ture表示随机终点，false表示使用start字段生成固定终点位置。
end_random=true

# 加速增益位置，仅在buff_random=false时生效，不能与起点位置和终点位置重复。
# 数组，包含两个元素，分别为xy坐标，取值范围为0~127。
buff=[80, 76]

# 加速增益是否随机。
# 布尔值，ture表示随机加速增益位置，false表示使用buff字段生成固定加速增益位置。
buff_random=true

# 加速增益冷却步数，加速增益在被收集后会进入冷却状态，冷却状态结束后重新激活。
# 整型，取值范围为1~2000。
buff_cooldown=100

# 技能冷却步数，技能在被施放后会进入冷却状态，冷却状态结束后重新激活。
# 整型，取值范围为100~2000。
talent_cooldown=100

# 宝箱是否随机, 布尔值，false - 固定宝箱，true - 随机宝箱。
# 若开启固定，则使用treasure_pos生成固定宝箱。
# 若开启随机，则使用treasure_count随机生成宝箱。
treasure_random=true

# 生成随机宝箱时的宝箱数量，仅在treasure_random = true时生效。
# 整型，取值范围为0~13。
treasure_count=8

# 生成固定宝箱时的宝箱位置，仅在treasure_random = false时生效。
# 二维数组，包含0~13个元素，每个元素为一个数组，分别为xy坐标，取值范围为0~127，元素不能重复。
treasure_pos=[]

# 是否生成随机动态障碍物。
# 布尔值，false表示固定动态障碍物，true表示随机生成一个动态障碍物。
obstacle_random=true

# 生成固定动态障碍物时的id，仅在obstacle_random = false时生效。
# 数组，包含0~6个元素，元素的取值范围为1~6，元素不能重复。
obstacle_id=[]

# 单局最大步数。
# 整型，取值范围为1~2000。
max_step=2000

# 英雄技能配置。
# 整型，1表示超级闪现技能，其他值非法。
talent_type=1
