#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


import torch
from kaiwu_agent.agent.base_agent import (
    predict_wrapper,
    exploit_wrapper,
    learn_wrapper,
    save_model_wrapper,
    load_model_wrapper,
    BaseAgent,
)
from agent_ppo.conf.conf import Config

import random
import numpy as np
from kaiwu_agent.utils.common_func import attached
from agent_ppo.model.model import NetworkModelActor
from agent_ppo.algorithm.algorithm import Algorithm
from agent_ppo.feature.definition import Sam<PERSON><PERSON><PERSON>, Obs<PERSON><PERSON>, ActD<PERSON>, SampleManager
from agent_ppo.feature.preprocessor import Preprocessor


def random_choice(p):
    r = random.random() * sum(p)
    s = 0
    for i in range(len(p)):
        if r > s and r <= s + p[i]:
            return i, p[i]
        s += p[i]
    return len(p) - 1, p[len(p) - 1]


@attached
class Agent(BaseAgent):
    def __init__(self, agent_type="player", device=None, logger=None, monitor=None):
        super().__init__(agent_type, device, logger, monitor)

        self.device = device
        self.model = NetworkModelActor()
        self.algorithm = Algorithm(device=device, logger=logger, monitor=monitor)
        self.preprocessor = Preprocessor()
        self.sample_manager = SampleManager()
        self.win_history = []
        self.logger = logger
        
        # Load pre-trained model if specified
        if Config.LOAD_MODEL_ID:
            logger.info("Load pre-trained model.")
            self.__load_model(
                path="/data/projects/back_to_the_realm_v2/ckpt",
                id=Config.LOAD_MODEL_ID,
            )
        
        self.reset()

    def update_win_rate(self, is_win):
        self.win_history.append(is_win)
        if len(self.win_history) > 100:
            self.win_history.pop(0)
        return sum(self.win_history) / len(self.win_history) if len(self.win_history) > 10 else 0

    def _predict(self, obs, legal_action):
        with torch.no_grad():
            inputs = self.model.format_data(obs, legal_action)
            output_list = self.model(*inputs)

        np_output_list = []
        for output in output_list:
            np_output_list.append(output.numpy().flatten())

        return np_output_list

    def predict_process(self, obs, legal_action):
        obs = np.array([obs])
        legal_action = np.array([legal_action])
        probs, value = self._predict(obs, legal_action)
        return probs, value

    def observation_process(self, obs, extra_info=None, is_exploit=False):
        # 评估的时候只返回feature, legal_action
        if is_exploit:
            feature, legal_action = self.preprocessor.process([obs, extra_info], self.last_action, is_exploit=is_exploit)
            return ObsData(
                feature=feature,
                legal_action=legal_action,
            )
        # 训练的时候再返回reward
        else:
            feature, legal_action, reward = self.preprocessor.process([obs, extra_info], self.last_action, is_exploit=is_exploit)
            return ObsData(
                feature=feature,
                legal_action=legal_action,
                reward=reward,
            )

    @predict_wrapper
    def predict(self, list_obs_data):
        feature = list_obs_data[0].feature
        legal_action = list_obs_data[0].legal_action
        probs, value = self.predict_process(feature, legal_action)
        action, prob = random_choice(probs)
        return [ActData(probs=probs, value=value, action=action, prob=prob)]

    def action_process(self, act_data):
        self.last_action = act_data.action
        return act_data.action

    @exploit_wrapper
    def exploit(self, observation):
        obs_data = self.observation_process(observation["obs"], observation["extra_info"], is_exploit=True)
        feature = obs_data.feature
        legal_action = obs_data.legal_action
        probs, value = self.predict_process(feature, legal_action)
        action, prob = random_choice(probs)
        act = self.action_process(ActData(probs=probs, value=value, action=action, prob=prob))
        return act

    def reset(self):
        self.preprocessor.reset()
        self.last_prob = 0
        self.last_action = -1

    @learn_wrapper
    def learn(self, list_sample_data):
        self.algorithm.learn(list_sample_data)

    @save_model_wrapper
    def save_model(self, path=None, id="1"):
        # To save the model, it can consist of multiple files,
        # and it is important to ensure that each filename includes the "model.ckpt-id" field.
        # 保存模型, 可以是多个文件, 需要确保每个文件名里包括了model.ckpt-id字段
        model_file_path = f"{path}/model.ckpt-{str(id)}.pkl"
        torch.save(self.algorithm.model.state_dict(), model_file_path)
        self.logger.info(f"save model {model_file_path} successfully")

    @load_model_wrapper
    def load_model(self, path=None, id="1"):
        self.__load_model(path, id)

    def __load_model(self, path=None, id="1"):
        model_file_path = f"{path}/model.ckpt-{str(id)}.pkl"
        try:
            checkpoint = torch.load(
                model_file_path,
                map_location=self.device
            )
            # Handle both direct state_dict and checkpoint with model_state_dict key
            if isinstance(checkpoint, dict) and "model_state_dict" in checkpoint:
                state_dict = checkpoint["model_state_dict"]
            else:
                state_dict = checkpoint
            
            self.model.load_state_dict(state_dict)
            self.algorithm.model.load_state_dict(state_dict)
            self.logger.info(f"load model {model_file_path} successfully")
        except FileNotFoundError:
            self.logger.info(f"File {model_file_path} not found")
